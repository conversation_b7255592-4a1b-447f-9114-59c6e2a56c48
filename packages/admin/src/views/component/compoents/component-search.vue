<template>
    <site-selector />
    <el-form
        inline
        label-width="100"
        :label-suffix="':'"
        :size="'default'"
        class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.delFlag"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple
                        @change="handleSearch">
                        <el-option
                            v-for="item in delFlagOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="组织">
                    <el-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in orgOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { useSiteStore } from '@/stores';
    import { ComponentSearchForm } from '@/types';
    import { dimensionApi } from '@/api/dimension-api.ts';

    // 事件
    const emit = defineEmits(['search']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // 删除状态枚举
    const delFlagOption = ref<{ name: any; code: number; }[]>(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 查询表单
    const searchForm = ref<Partial<ComponentSearchForm>>({
        siteCode: '',
        delFlags: [0],
        name: '',
        orgIds: []
    });

    // 查询
    const handleSearch = () => {
        emit('search', searchForm.value);
    };

    // 监听 siteStore
    watch(() => siteStore.currentSiteCode, () => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
    });

    // 监听查询表单
    watch(() => searchForm.value, () => {
        console.log("aaaa:"+ searchForm.value.orgIds)
        handleSearch();
    }, { deep: true });

    onMounted(() => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
        handleSearch();
        getOrgOptions();
    });
</script>
