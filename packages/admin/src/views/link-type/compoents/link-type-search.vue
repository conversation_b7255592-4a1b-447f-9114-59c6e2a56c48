<template>
    <el-form inline label-width="100" :label-suffix="':'" :size="'default'" class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="数据类型">
                    <el-select
                        v-model="searchForm.dataType"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        @change="handleSearch"
                    >
                        <el-option
                            v-for="item in dataTypeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.status"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        @change="handleSearch"
                    >
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="组织">
                    <el-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in orgOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.name" placeholder="请输入" style="width: 180px" clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { LinkTypeSearchForm } from '@/types';
    import { Enumeration, useEnumStore, LabelValue } from '@chances/portal_common_core';
    import { dimensionApi } from '@/api/dimension-api.ts';

    // 事件
    const emit = defineEmits(['search']);

    // pinia store
    const enumStore = useEnumStore();

    // 跳转链接查询表单
    const searchForm = ref<Partial<LinkTypeSearchForm>>({});

    // 状态枚举值
    const statusOptions = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 数据类型枚举值
    const dataTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('dataType') || []);

    // 处理搜索事件
    const handleSearch = () => {
        emit('search', searchForm.value);
    };

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // 监听查询表单
    watch(() => searchForm.value, () => {
        handleSearch();
    }, { deep: true });

    onMounted(()=> {
        getOrgOptions()
    })
</script>
