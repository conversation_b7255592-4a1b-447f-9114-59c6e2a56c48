<template>
    <el-form
        inline
        label-width="100"
        :label-suffix="':'"
        :size="'default'"
        class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlag"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable>
                        <el-option
                            v-for="item in delFlagOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.status"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable>
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="组织">
                    <el-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in orgOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon
                                class="el-input__icon"
                                @click="emit('search')">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { computed, ref, onMounted } from 'vue';
    import { SiteSearchForm } from '@/types';
    import { useEnumStore, LabelValue } from '@chances/portal_common_core';
    import { dimensionApi } from '@/api/dimension-api.ts';
    // 参数
    const props = defineProps<{
        modelValue: Partial<SiteSearchForm>
    }>();

    const emit = defineEmits<{
        // 送审当前
        (e: 'update:modelValue', item: Partial<SiteSearchForm>): void;
        (e: 'search'): void;
    }>();

    // pinia store
    const enumStore = useEnumStore();

    // 删除状态枚举
    const delFlagOption = ref<{ name: any; code: number; }[]>(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 可用禁用状态枚举
    const enableStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 查询表单
    const searchForm = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value),
    });

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // 组件挂载时请求数据
    onMounted(() => {
        getOrgOptions();
    });
</script>
