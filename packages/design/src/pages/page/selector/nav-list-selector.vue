<template>
    <el-table :data="navList"
              :row-key="(row: Nav) => row.code"
              :header-cell-style="{ color: 'black', height: '40px' }"
              :tree-props="{ children: 'children' }"
              style="width: 100%"
              highlight-current-row
              :current-row-key="selectedRow"
    >
        <el-table-column property="title" label="导航名称" width="140" show-overflow-tooltip>
            <template #default="{ row }">
                {{ row.title }} {{ row.children?.length ? '（' + row.children.length + '）' : '' }}
            </template>
        </el-table-column>
        <el-table-column property="pageCode" label="页面名称" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
                <el-button type="primary" link @click="onClickChangePage(row)">
                    {{ pageList.find(item => item.code === row.pageCode)?.name }}
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
    import { usePageDesignerStore } from '@/stores';
    import { onMounted, ref } from 'vue';
    import { navApi, pageApi } from '@/utils/http/register.ts';
    import { Nav, Page } from '@/types';

    // 导航选择器，当从导航跳转过来的时候，需要展示桌面下的所有导航与对应的页面
    defineOptions({
        name: 'NavListSelector',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 选中行
    const selectedRow = ref('');

    // 导航列表
    const navList = ref<Nav[]>([]);

    // 页面列表
    const pageList = ref<Page[]>([]);

    // 点击切换页面
    const onClickChangePage = async (row: Nav) => {
        // 获取当前路径和 hash
        const path = window.location.pathname;
        const hash = window.location.hash;

        // // 生成新的 query 字符串
        const newQuery = {
            mode: 'page',
            desktopCode: row.ownerPageCode,
            navCode: row.code,
            pageCode: row.pageCode,
        };
        const search = new URLSearchParams(newQuery).toString();

        // 拼接新地址
        const newUrl = `${path}?${search}${hash}`;

        // 修改地址栏、加入历史记录、不刷新页面
        window.history.replaceState(null, '', newUrl);

        // 刷新页面
        pageDesignerStore.switchMode('page');
        pageDesignerStore.setNavAndDesktop(row.code, row.ownerPageCode);
        await pageDesignerStore.refreshPage(row.pageCode);
    };

    // 根据桌面编码，获取导航列表
    const getNavList = async () => {
        const res = await navApi.findNavTree({
            siteCode: pageDesignerStore.page.siteCode,
            desktopCode: pageDesignerStore.desktopCode,
            delFlag: 0,
            status: 1,
        });

        if (res.code === 200) {
            const processTree = (nodes: Nav[]) => {
                nodes.forEach((node: any) => {
                    node.children = node.children || [];
                    if (node.children.length > 0) {
                        processTree(node.children);
                    }
                });
            };

            navList.value = res.result;
            processTree(navList.value);
        }
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList({ siteCode: pageDesignerStore.page.siteCode }, { paged: false });
        if (res.code === 200) {
            pageList.value = res.result;
        }
    };

    onMounted(() => {
        getPageList();
        getNavList();

        selectedRow.value = pageDesignerStore.navCode;
    });
</script>
