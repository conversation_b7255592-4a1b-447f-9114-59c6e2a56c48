<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-2 border-b border-gray-200">
            <el-input v-model="searchForm.name" placeholder="搜索楼层定义" class="w-full" size="default" clearable>
                <template #prefix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>

            <tags-selector class="mt-2 mb-1" size="default" :tags="tagOptions" v-model="tagsList" />
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 p-4 w-full flex-1 overflow-y-auto">
            <div
                v-for="(layout, index) in sectionList"
                :key="index"
                class="border border-gray-200 rounded-md bg-white cursor-pointer hover:shadow-md transition-all relative flex flex-col overflow-hidden select-none"
                :class="{ 'border-blue-500 ring-2 ring-blue-200': activeSectionIndex === index, 'active:bg-gray-50': isDraggingIndex === index }"
                :draggable="draggable"
                @dragstart="(e) => onDragStart(index, layout, e)"
                @dragend="onDragEnd"
                @click="selectSection(index)"
            >
                <div class="relative aspect-[2/1] overflow-hidden w-full">
                    <el-image
                        :src="layout.icon"
                        fit="cover"
                        class="w-full h-full object-cover cursor-pointer"
                        :preview-src-list="[layout.icon]"
                        hide-on-click-modal
                        preview-teleported
                        :z-index="3000"
                        @dragstart.prevent
                    >
                        <template #error>
                            <image-error-fallback text="图片损坏" />
                        </template>
                    </el-image>
                </div>
                <div class="px-2 py-2 w-full flex justify-center items-center">
                    <span class="truncate text-sm font-medium w-full text-center"
                          :title="layout.name">{{ layout.name }}</span>
                </div>
            </div>
            <div v-if="sectionList.length === 0" class="col-span-2 text-center py-8 text-gray-400">
                <el-empty description="暂无楼层定义" :image-size="60" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { Search } from '@element-plus/icons-vue';
    import { computed, onMounted, ref, watch } from 'vue';
    import { LabelValue, usePermissionStore } from '@chances/portal_common_core';
    import { SectionSearchForm, Section } from '@/types';
    import { usePageDesignerStore, useSectionListStore } from '@/stores';
    import ImageErrorFallback from '@/components/image-error-fallback.vue';
    import { DESIGN_BIZ_PERMISSION } from '@/utils/permission-list.ts';
    import { canEdit } from '@/utils/publish-status-control.ts';

    // 楼层定义列表选择器
    defineOptions({
        name: 'SectionListSelector',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const sectionListStore = useSectionListStore();
    const permissionStore = usePermissionStore();

    // 楼层定义查询表单
    const searchForm = ref<Partial<SectionSearchForm>>({
        siteCode: pageDesignerStore.page.siteCode,
        resolution: pageDesignerStore.page.resolution,
        delFlag: 0,
        status: 1,
        type: pageDesignerStore.mode === 'page' ? 'page' : 'top'
    });

    // 标签选项列表
    const tagOptions = ref<LabelValue[]>([
        {
            label: '1',
            value: '1',
        },
        {
            label: '2',
            value: '2',
        },
        {
            label: '3',
            value: '3',
        },
        {
            label: '4',
            value: '4',
        },
        {
            label: '5',
            value: '5',
        },
        {
            label: '6+',
            value: '6+',
        },
    ]);

    // 标签列表
    const tagsList = ref<string[]>([]);

    // 楼层定义列表
    const sectionList = ref<Section[]>([]);

    // 当前楼层定义索引
    const activeSectionIndex = ref<number | null>(null);

    // 拖拽楼层定义索引
    const isDraggingIndex = ref<number | null>(null);

    // 是否有权限
    const hasPermission = computed<boolean>(() => {
        // 校验是否有桌面或页面编辑权限
        if (pageDesignerStore.mode === 'page') {
            // 当前为页面，如果没有页面操作权限，不可拖拽
            if (!canEdit(pageDesignerStore.page) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE.EDIT, {
                type: 'org',
                value: pageDesignerStore.page.orgId,
            })) {
                return false;
            }
        } else if (pageDesignerStore.mode === 'desktop') {
            // 当前为桌面，如果没有桌面操作权限，不可拖拽
            if (!canEdit(pageDesignerStore.page) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.DESKTOP.EDIT, {
                type: 'org',
                value: pageDesignerStore.page.orgId,
            })) {
                return false;
            }
        }
        return true;
    })

    // 是否可拖拽
    const draggable = computed<boolean>(() => {
        // 校验是否有桌面或页面编辑权限
        if (!hasPermission.value) {
            // 如果没有权限，直接返回 false
            return false;
        }

        if (pageDesignerStore.mode === 'page') {
            // 页面模式下，都可以拖拽
            return true;
        }
        // 桌面模式下，只有未删除的页面楼层数量小于 1 才可以可拖拽，确保桌面顶部楼层只有一个
        return pageDesignerStore.page.pageSectionList.filter(pageSection => pageSection.delFlag === 0).length < 1;
    });

    // 获取楼层定义列表
    const getSectionList = async () => {
        searchForm.value.tagsList = tagsList.value;
        if (tagsList.value.indexOf('-1') !== -1) {
            searchForm.value.tagsList = [];
        }

        const res = await sectionListStore.getSectionListByParams(searchForm.value, {
            paged: false,
            sort: 'orderNo,asc;id,desc',
        });
        sectionList.value = res.result;
    };

    // 选择楼层定义
    const selectSection = (index: number) => {
        activeSectionIndex.value = index;
    };

    // 拖拽楼层定义开始
    const onDragStart = (index: number, section: Section, event: DragEvent) => {
        isDraggingIndex.value = index;
        event.dataTransfer?.setData('section', JSON.stringify(section));
        event.dataTransfer?.setData('application/json+section', 'section');
        event.dataTransfer!.effectAllowed = 'copy';
    };

    // 拖拽楼层定义结束
    const onDragEnd = () => {
        isDraggingIndex.value = null;
    };

    // 监听名称
    watch(() => searchForm.value.name, () => {
        getSectionList();
    });
    // 监听 tags
    watch(() => tagsList, () => {
        getSectionList();
    }, { immediate: true, deep: true });

    onMounted(() => {
        getSectionList();
    });
</script>
